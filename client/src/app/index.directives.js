'use strict';

var TEMPLATE = '<div class="png-time form-control" ng-click="focus()">' +
  '<input type="text" name="hour" class="hour" maxlength="2" ng-model="hour" />' +
  '<span>:</span>' +
  '<input type="text" name="minute" class="minute" maxlength="2" ng-model="minute" />' +
  '<input type="text" name="mode" class="mode" maxlength="2" ng-model="mode" ng-show="timeMode !== 24" />' +
  '</div>';

angular.module('app')

  .directive('pngTimeInput', function () {
    return {
      restrict: 'E',
      replace: true,
      template: TEMPLATE,
      scope: {
        model: '=',
        timeMode: '='
      },
      link: timepickerLinkFn
    };

    function timepickerLinkFn(scope, element, attr) {
      var hourInput, minuteInput, modeInput,
        hourInputEl, minuteInputEl, modeInputEl,
        hourInputCtrl, minuteInputCtrl, modeInputCtrl,
        currentFocusEl,
        keyCount = 2;

      //default time mode to 12h
      var timeMode = scope.timeMode = scope.timeMode !== 24 ? 12 : 24;

      function hourInputParser(value) {
        var hour = parseInt(value);

        if (isNaN(hour)) {
          hourInputCtrl.$viewValue = '00';
          hourInputCtrl.$render();
          hourInput.select();
          return hourInputCtrl.$viewValue;
        }

        // Some logic If we're in military time and the user wants to input 00
        if (timeMode === 24 && hour === 0) {
          // Check for '10' input
          if (parseInt(hourInputCtrl.$modelValue) === 0) {
            hourInputCtrl.$viewValue = '00';
            hourInputCtrl.$commitViewValue();
            hourInputCtrl.$render();
            minuteInput.focus();
            minuteInput.select();
            return hourInputCtrl.$viewValue;
          }
        }

        //if user meant to type 10, 11 or 12 for standard or 10 - 23 for military
        var squashTime = false;
        if (timeMode === 24) {
          if (parseInt(hourInputCtrl.$modelValue) === 2 && hour < 4) {
            squashTime = true;
          } else if (parseInt(hourInputCtrl.$modelValue) < 2 && hour < 10) {
            squashTime = true;
          }
        } else {
          if (parseInt(hourInputCtrl.$modelValue) < 2 && hour < 3) {
            squashTime = true;
          }
        }
        if (squashTime) {
          hourInputCtrl.$viewValue = parseInt(hourInputCtrl.$modelValue) + '' + hour;
          hourInputCtrl.$commitViewValue();
          hourInputCtrl.$render();
          minuteInput.focus();
          minuteInput.select();
          return hourInputCtrl.$viewValue;
        }

        if (hour < 10 && value.length === 1) {
          hourInputCtrl.$viewValue = '0' + value;
          hourInputCtrl.$commitViewValue();
          hourInputCtrl.$render();
        }

        if (hour > (timeMode === 24 ? 2 : 1)) {
          minuteInput.focus();
          minuteInput.select();
        } else {
          hourInput.select();
        }

        return hourInputCtrl.$viewValue;
      }

      function minuteInputParser(value) {
        var minute = parseInt(value);

        if (isNaN(minute)) {
          minuteInputCtrl.$viewValue = '00';
          minuteInputCtrl.$render();
          minuteInput.select();
          return minuteInputCtrl.$viewValue;
        }

        if (parseInt(minuteInputCtrl.$modelValue) < 6 && minute < 10) {
          minuteInputCtrl.$viewValue = parseInt(minuteInputCtrl.$modelValue) + '' + minute;
          minuteInputCtrl.$commitViewValue();
          minuteInputCtrl.$render();
          if (timeMode === 12) {
            modeInput.focus();
            modeInput.select();
          }
          minuteInput.select();
          return minuteInputCtrl.$viewValue;
        }

        if (minute < 10 && value.length === 1) {
          minuteInputCtrl.$viewValue = '0' + value;
          minuteInputCtrl.$commitViewValue();
          minuteInputCtrl.$render();
        }

        if (parseInt(value) > 5 && timeMode === 12) {
          modeInput.focus();
          modeInput.select();
        } else {
          minuteInput.focus();
          minuteInput.select();
        }

        return minuteInputCtrl.$viewValue;
      }

      function modeInputParser(value) {
        //if user has typed 'a', or has typed 'm' with 'AM' showing
        if ((value.toLowerCase().indexOf('a') >= 0) || (value.toLowerCase() === 'm' && modeInputCtrl.$modelValue === 'AM')) {
          modeInputCtrl.$viewValue = 'AM';
        } else {
          modeInputCtrl.$viewValue = 'PM';
        }
        modeInputCtrl.$commitViewValue();
        modeInputCtrl.$render();

        modeInput.select();

        return modeInputCtrl.$viewValue;
      }

      function resetKeyCount() {
        keyCount = 2;
      }

      function inputBlurHandler() {
        element.removeClass('time-focus');

        if (currentFocusEl === this) {// jshint ignore:line
          currentFocusEl = null;
        }
      }

      function inputFocusHandler() {
        element.addClass('time-focus');
        currentFocusEl = this;// jshint ignore:line
      }

      function modelWatcher(dt) {
        if (!dt) {
          scope.hour = '--';
          scope.minute = '--';
          scope.mode = '--';
          return;
        }

        if (angular.isString(dt)) {
          dt = new Date(dt);
        }

        if (angular.isDate(dt)) {
          var tempHour = dt.getHours();
          scope.minute = dt.getMinutes();

          if (timeMode === 12) {
            if (tempHour > 11) {
              scope.hour = tempHour === 12 ? tempHour : tempHour - 12;
              scope.mode = 'PM';
            } else {
              scope.hour = tempHour === 0 ? 12 : tempHour;
              scope.mode = 'AM';
            }
          } else {
            scope.hour = tempHour === 0 ? 0 : tempHour; // Default to 00 for military time
          }
          if (scope.hour < 10) {
            scope.hour = '0' + scope.hour;
          }
          if (scope.minute < 10) {
            scope.minute = '0' + scope.minute;
          }
        }
      }

      angular.forEach(element.find('input'), function (input) {
        if (input.name === 'hour') {
          hourInput = input;
          hourInputEl = angular.element(input);
          hourInputCtrl = hourInputEl.controller('ngModel');
        } else if (input.name === 'minute') {
          minuteInput = input;
          minuteInputEl = angular.element(input);
          minuteInputCtrl = minuteInputEl.controller('ngModel');
        } else if (input.name === 'mode') {
          modeInput = input;
          modeInputEl = angular.element(input);
          modeInputCtrl = modeInputEl.controller('ngModel');
        }
      });

      scope.$watch('model', modelWatcher);

      scope.$watch('timeMode', function (value) {
        timeMode = value;
        modelWatcher(scope.model);
      });

      scope.$watch(function () {
        var hour = parseInt(scope.hour);
        var minute = parseInt(scope.minute);

        if (isNaN(hour) || isNaN(minute)) {
          return null;
        }

        if (timeMode === 12) {
          if (scope.mode === 'AM') {
            return (hour === 12 ? 0 : hour) + ':' + minute;
          } else {
            return (hour === 12 ? hour : hour + 12) + ':' + minute;
          }
        } else {
          return hour + ':' + minute;
        }
      },
        function inputWatcher(value) {
          if (!value) {
            return;
          }

          var dateParts = value.split(':');

          if (!scope.model) {
            scope.model = new Date();
          }

          scope.model.setHours(dateParts[0]);
          scope.model.setMinutes(dateParts[1]);
          scope.model.setSeconds(0);
        });

      scope.focus = function elementFocus() {
        if (document.activeElement !== minuteInput && document.activeElement !== modeInput) {
          hourInput.focus();
          hourInput.select();
        }
      };

      hourInputCtrl.$parsers.unshift(hourInputParser);
      minuteInputCtrl.$parsers.unshift(minuteInputParser);
      modeInputCtrl.$parsers.unshift(modeInputParser);

      hourInputEl.on('focus', resetKeyCount);
      minuteInputEl.on('focus', resetKeyCount);
      hourInputEl.on('focus', inputFocusHandler);
      minuteInputEl.on('focus', inputFocusHandler);
      modeInputEl.on('focus', inputFocusHandler);

      hourInputEl.on('blur', inputBlurHandler);
      minuteInputEl.on('blur', inputBlurHandler);
      modeInputEl.on('blur', inputBlurHandler);

      hourInputEl.on('keydown', function (evt) {
        keyCount--;

        switch (evt.keyCode) {
          case 37: //left key
            evt.preventDefault();
            evt.stopPropagation();
            break;
          case 38://up key
          case 40://down key
            evt.preventDefault();
            evt.stopPropagation();
            break;
          case 39: //right key
            evt.preventDefault();
            evt.stopPropagation();
            minuteInput.focus();
            minuteInput.select();
            break;
        }
      });
      minuteInputEl.on('keydown', function (evt) {
        keyCount--;

        switch (evt.keyCode) {
          case 37: //left key
            evt.preventDefault();
            evt.stopPropagation();
            hourInput.focus();
            hourInput.select();
            break;
          case 38://up key
          case 40://down key
            evt.preventDefault();
            evt.stopPropagation();
            break;
          case 39: //right key
            evt.preventDefault();
            evt.stopPropagation();
            modeInput.focus();
            modeInput.select();
            break;
        }
      });
      modeInputEl.on('keydown', function (evt) {
        keyCount--;

        switch (evt.keyCode) {
          case 37: //left key
            evt.preventDefault();
            evt.stopPropagation();
            minuteInput.focus();
            minuteInput.select();
            break;
          case 38://up key
          case 40://down key
            evt.preventDefault();
            evt.stopPropagation();
            break;
          case 39: //right key
            evt.preventDefault();
            evt.stopPropagation();
            break;
        }
      });

      hourInputEl.on('click', function () {
        hourInput.select();
        //safari
        hourInput.setSelectionRange(0, 2)
      });
      minuteInputEl.on('click', function () {
        minuteInput.select();
        //safari
        minuteInput.setSelectionRange(0, 2)
      });
      modeInputEl.on('click', function () {
        modeInput.select();
        //safari
        modeInput.setSelectionRange(0, 2)
      });

      scope.$on('$destroy', function () {
        hourInputEl.off('click');
        hourInputEl.off('focus');
        hourInputEl.off('keydown');
        hourInputEl.off('blur');
        minuteInputEl.off('click');
        minuteInputEl.off('focus');
        minuteInputEl.off('keydown');
        minuteInputEl.off('blur');
        modeInputEl.off('blur');
        modeInputEl.off('click');
        modeInputEl.off('keydown');
        modeInputEl.off('blur');
      });
    }
  })
  .directive('moneyMask', moneyMask)

  .directive('ngReallyClick', ['$uibModal',
    function ($uibModal) {

      var ModalInstanceCtrl = function ($scope, $uibModalInstance) {
        $scope.ok = function () {
          $uibModalInstance.close();
        };

        $scope.cancel = function () {
          $uibModalInstance.dismiss('cancel');
        };
      };

      //Corrige erro ao minificar a diretiva
      ModalInstanceCtrl.$inject = ['$scope', '$uibModalInstance'];

      return {
        restrict: 'A',
        scope: {
          ngReallyClick: "&"
        },
        link: function (scope, element, attrs) {
          element.bind('click', function () {
            var message = attrs.ngReallyMessage || "Tem certeza que deseja desativar este ítem ?";

            var modalHtml = '<div class="modal-body" style="z-index: 2000;">' + message + '</div>';
            modalHtml += '<div class="modal-footer"><button class="btn btn-primary" ng-click="ok()">Sim</button><button class="btn btn-warning" ng-click="cancel()">Não</button></div>';

            var modalInstance = $uibModal.open({
              template: modalHtml,
              controller: ModalInstanceCtrl
            });

            modalInstance.result.then(function () {
              scope.ngReallyClick();
            }, function () {
              //Modal dismissed
            });

          });

        }
      };
    }
  ])

  .directive('copyToClipboard', function () {
    return {
      restrict: 'A',
      link: function (scope, elem, attrs) {
        // Adiciona automaticamente a classe copyable-field
        elem.addClass('copyable-field');

        // Adiciona automaticamente o title se não existir
        if (!attrs.title) {
          elem.attr('title', 'Clique para copiar');
        }

        elem.click(function (event) {
          if (attrs.copyToClipboard) {
            var textToCopy = attrs.copyToClipboard;

            // Função para mostrar o toast
            function showCopyToast(success, text) {
              var toast = $('<div class="copy-toast">' +
                (success ? 'Copiado: ' + text : 'Erro ao copiar') +
                '</div>');

              // Estilos inline para o toast
              toast.css({
                'position': 'fixed',
                'background-color': success ? '#28a745' : '#dc3545',
                'color': 'white',
                'padding': '8px 12px',
                'border-radius': '4px',
                'font-size': '12px',
                'z-index': '9999',
                'pointer-events': 'none',
                'opacity': '0',
                'transition': 'opacity 0.3s ease',
                'max-width': '200px',
                'word-wrap': 'break-word',
                'box-shadow': '0 2px 8px rgba(0,0,0,0.2)'
              });

              // Posiciona o toast próximo ao cursor
              var x = event.pageX + 10;
              var y = event.pageY - 30;

              // Ajusta posição se estiver muito próximo das bordas
              if (x + 200 > $(window).width()) {
                x = event.pageX - 210;
              }
              if (y < 10) {
                y = event.pageY + 10;
              }

              toast.css({
                'left': x + 'px',
                'top': y + 'px'
              });

              $('body').append(toast);

              // Animação de entrada
              setTimeout(function() {
                toast.css('opacity', '1');
              }, 10);

              // Remove o toast após 2 segundos
              setTimeout(function() {
                toast.css('opacity', '0');
                setTimeout(function() {
                  toast.remove();
                }, 300);
              }, 700);
            }

            // Tenta usar a API moderna do clipboard
            if (navigator.clipboard && window.isSecureContext) {
              navigator.clipboard.writeText(textToCopy).then(function() {
                showCopyToast(true, textToCopy);
              }).catch(function(err) {
                console.error('Erro ao copiar texto:', err);
                // Fallback para o método antigo
                fallbackCopyTextToClipboard(textToCopy);
              });
            } else {
              // Fallback para navegadores mais antigos
              fallbackCopyTextToClipboard(textToCopy);
            }

            function fallbackCopyTextToClipboard(text) {
              var $temp_input = $("<input>");
              $("body").append($temp_input);
              $temp_input.val(text).select();
              try {
                var success = document.execCommand("copy");
                showCopyToast(success, text);
              } catch (err) {
                console.error('Erro ao copiar texto (fallback):', err);
                showCopyToast(false, text);
              }
              $temp_input.remove();
            }
          }
        });
      }
    };
  })

  .directive('formatMac', function () {
    return {
      restrict: 'A',
      link: function (scope, elem, attrs) {
        // Função para formatar o MAC
        function formatMacAddress(mac) {
          if (!mac) return '';

          // Remove todos os caracteres que não são A-F, a-f ou 0-9 (equivalente ao REGEXP_REPLACE)
          var cleanMac = mac.replace(/[^A-Fa-f0-9]/g, '');

          // Se não tem 12 caracteres, retorna como está
          if (cleanMac.length !== 12) {
            return mac; // Retorna o original se não for um MAC válido
          }

          // Formata no padrão XX:XX:XX:XX:XX:XX
          return cleanMac.replace(/(.{2})/g, '$1:').slice(0, -1);
        }

        // Observa mudanças no atributo format-mac
        attrs.$observe('formatMac', function(value) {
          if (value) {
            var formattedMac = formatMacAddress(value);
            elem.text(formattedMac);
          }
        });

        // Se o valor inicial já existe, formata imediatamente
        if (attrs.formatMac) {
          var initialFormatted = formatMacAddress(attrs.formatMac);
          elem.text(initialFormatted);
        }
      }
    };
  })

  .directive('authorize', ['$compile', 'AuthorizationService', function ($compile, AuthorizationService) {
    return {
      restrict: 'A',
      replace: true,
      link: function ($scope, element, attributes) {

        var scopes = attributes.authorize;
        var authorized = AuthorizationService.Authorize(scopes);
        var el = angular.element(element);
        el.attr('ng-if', authorized);
        //remove the attribute, otherwise it creates an infinite loop.
        el.removeAttr('authorize');
        $compile(el)($scope);
      }
    };
  }
  ])

  .directive('authorizeDisable', ['$compile', 'AuthorizationService', function ($compile, AuthorizationService) {
    return {
      restrict: 'A',
      replace: true,
      link: function ($scope, element, attributes) {
        var scopes = attributes.authorizeDisable;
        var authorized = AuthorizationService.Authorize(scopes);
        var el = angular.element(element);
        el.attr('disabled', authorized);
        //remove the attribute, otherwise it creates an infinite loop.
        el.removeAttr('authorize-disable');
        $compile(el)($scope);
      }
    };
  }
  ])

  .directive('authorizeEdit', ['$compile', 'AuthorizationService', function ($compile, AuthorizationService) {
    return {
      restrict: 'A',
      replace: true,
      link: function ($scope, element, attributes) {
        var scopes = attributes.authorizeEdit;
        var authorized = AuthorizationService.Authorize(scopes);
        var el = angular.element(element);
        el.attr('disabled', !authorized);
        //remove the attribute, otherwise it creates an infinite loop.
        el.removeAttr('authorize-edit');
        $compile(el)($scope);
      }
    };
  }
  ])

  .directive('stringToNumber', function () {
    return {
      require: 'ngModel',
      link: function (scope, element, attrs, ngModel) {
        ngModel.$parsers.push(function (value) {
          return '' + value;
        });
        ngModel.$formatters.push(function (value) {
          return parseFloat(value);
        });
      }
    };
  })

  .directive('ngClickCopy', ['ngCopy', function (ngCopy) {
    return {
      restrict: 'A',
      link: function (scope, element, attrs) {
        element.bind('click', function (e) {
          ngCopy(attrs.ngClickCopy);
        });
      }
    }
  }]);


function moneyMask($filter, $window) {
  var directive = {
    require: 'ngModel',
    link: link,
    restrict: 'A',
    scope: {
      model: '=ngModel'
    }
  };
  return directive;

  function link(scope, element, attrs, ngModelCtrl) {
    var display, cents;

    ngModelCtrl.$render = function () {
      display = $filter('number')(cents / 100, 2);

      if (attrs.moneyMaskPrepend) {
        display = attrs.moneyMaskPrepend + ' ' + display;
      }

      if (attrs.moneyMaskAppend) {
        display = display + ' ' + attrs.moneyMaskAppend;
      }

      element.val(display);
    }

    scope.$watch('model', function onModelChange(newValue) {
      newValue = parseFloat(newValue) || 0;

      if (newValue !== cents) {
        cents = Math.round(newValue * 100);
      }

      ngModelCtrl.$viewValue = newValue;
      ngModelCtrl.$render();
    });

    element.on('keydown', function (e) {
      if ((e.which || e.keyCode) === 8) {
        cents = parseInt(cents.toString().slice(0, -1)) || 0;

        ngModelCtrl.$setViewValue(cents / 100);
        ngModelCtrl.$render();
        scope.$apply();
        e.preventDefault();
      }
    });

    element.on('keypress', function (e) {
      var key = e.which || e.keyCode;

      if (key === 9) {
        return true;
      }

      if (key >= 96 && key <= 105) {
        key -= 48; // Numpad keys
      }

      var char = String.fromCharCode(key);
      e.preventDefault();

      if (char.search(/[0-9\-]/) === 0) {
        cents = parseInt(cents + char);
      }
      else {
        return false;
      }

      if (e.currentTarget.selectionEnd != e.currentTarget.selectionStart) {
        ngModelCtrl.$setViewValue(parseInt(char) / 100);
      }
      else {
        ngModelCtrl.$setViewValue(cents / 100);
      }
      ngModelCtrl.$render();
      scope.$apply();
    })
  }
}
