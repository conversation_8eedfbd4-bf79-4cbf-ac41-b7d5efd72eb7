@media only screen and (min-width: 600px) {
.full {

    width:100%;
    height:100%;
    min-height:100%;
}

.center-screen{
  padding:10px;
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.copyright {
  margin-top: 10px;
}

.nya-bs-select.btn-group .dropdown-menu.inner {
    min-height: 180px;
}

body {
    font-size: 11px;
    padding-top: 85px;
    height:100%;
}

.main {
    padding: 0px;
    padding-left: 20px;
    padding-right: 20px;
    height: 100%;
}

.navbar-nav > li > a, .navbar-brand {
    padding-top:4px !important;
    padding-bottom:0 !important;
    height: 28px;
}
.navbar {
    min-height:2px !important;
}

.sub-nav {
    top: 28px;
    z-index:1004;
}

.sub-nav-evento {
    top: 60px;
    min-height: 25px;
    z-index: 1003;
}

.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
    padding: 1px;
}

table tr td:first-child::after {

   display: inline-block;
   vertical-align: middle;
   min-height: 22px;
}

.label {
    font-size: 90%;
}

.scrollable-menu {
    height: auto;
    max-height: 350px;
    overflow-x: hidden;
}

.sub-nav-laranja {
    z-index: 1004;
    top: 58px;
    max-height: 2px;
    background-color: #fcf8e3;
    border-color: #fbeed5;
    color: #c09853;
    background-image: -webkit-linear-gradient(#E0A600, #FF9A03 60%, #F7C200);
}

#loading-bar-spinner {
  top: 50%;
  left: 50%;
}

#loading-bar-spinner .spinner-icon {
  width: 80px;
  height: 80px;
  border:  solid 5px transparent;
  border-top-color:  #86C3E6;
  border-left-color: #86C3E6;
  border-radius: 50%;

  -webkit-animation: loading-bar-spinner 400ms linear infinite;
  -moz-animation:    loading-bar-spinner 400ms linear infinite;
  -ms-animation:     loading-bar-spinner 400ms linear infinite;
  -o-animation:      loading-bar-spinner 400ms linear infinite;
  animation:         loading-bar-spinner 400ms linear infinite;
}

#loading-bar .bar {
  -webkit-transition: width 350ms;
  -moz-transition: width 350ms;
  -o-transition: width 350ms;
  transition: width 350ms;

  background: #86C3E6;
  position: fixed;
  z-index: 1000;
  top: 83px;
  left: 0;
  width: 100%;
  height: 2px;
  border-bottom-right-radius: 1px;
  border-top-right-radius: 1px;
}

h4, .h4 {
    font-size: 14px;
    font-weight: bold;
}

.modal-header {
    padding: 8px;
}

.modal-body {
    position: relative;
    padding: 20px 20px 3px 20px;
}

.modal-footer {
    padding: 10px;
}

.form-control {
    height: 28px;
    padding: 2px 12px;
    font-size: 10px;
}

.btn {
    font-size: 11px;
    max-height: 28px;
    padding-top: 5px;
}



.btn-xs, .btn-group-xs>.btn {
    padding: 1px 3px;
    font-size: 10px;
    line-height: 1.5;
    border-radius: 3px;
}

.btn-sm, .btn-group-sm>.btn {
    padding: 3px 7px;
}
.angular-ui-tree-handle {
    background: none;
    border: 0px;
    color: #7c9eb2;
    padding: 2px 2px;
    font-weight: normal;
}

.btn-group .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.form-group {
    margin-bottom: 10px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    font-size: 11px;
    text-align: left;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0,0,0,0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,0.175);
    box-shadow: 0 6px 12px rgba(0,0,0,0.175);
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}

.navegacao {
    margin-top: 5px;
    list-style: none;
}

.breadcrumb {
    background-color: transparent;
    margin-bottom: 8px;
    margin-top: 4px;
    padding-bottom: 0px;
    padding-top: 0px;
    padding-left: 2px;
}

h6, .h6 {
    margin-bottom: 1px;
}

h5 {
    font-size: 13px;
    font-weight: bold;
}

.barra {
    padding: 1px 10px;
    margin: 5px 0px 5px 0px;
    background: #E6E6E6;
    border-radius: 4px;
    min-height: 53px;
}

.barra .form-group {
    margin: 4px;
}

.sub-barra {
    padding: 3px;
    margin: 5px 0px 5px 0px;
    background: #F7F7F7;
    border-radius: 4px;
    min-height: 23px;
}

.btn-incluir {
    min-height: 43px;
}

.navbar-brand {
  font-size: 16px;
  font-weight: bold;
}

.lead {
    margin-bottom: 5px;
    font-weight: 18px;
}

.logo{
    text-align: center;
    margin-top: 40px;
}

.badge {
    font-size: 10px;
    border-radius: 7px;
}
}
