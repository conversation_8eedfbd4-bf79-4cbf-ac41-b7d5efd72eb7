.blinking {
  -webkit-animation: 1s blink ease infinite;
  -moz-animation: 1s blink ease infinite;
  -ms-animation: 1s blink ease infinite;
  -o-animation: 1s blink ease infinite;
  animation: 1s blink ease infinite;
}

@keyframes blink {
  from, to {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

@-moz-keyframes blink {
  from, to {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

@-webkit-keyframes blink {
  from, to {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

@-ms-keyframes blink {
  from, to {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

@-o-keyframes blink {
  from, to {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.faded {
  background: rgba(0, 0, 0, 0.5);
}

.on-top {
  z-index: 99000 !important;
}

.warning-row {
  width: 100%;
  text-align: center;
  background: #fbf9e2;
  border: 1px solid #CCC;
  padding: 10px 0px;
}

.contrato-item {
  padding: 5px 10px;
  border-radius: 5px;
  width: 230px;
  border: 2px solid #000;
  margin-right: 15px;
}

.contrato-item:hover {
  background: #D9D9D9;
}

.contrato-item.selecionado {
  background: #3f76b8;
  color: #FFF;
  border-color: #39F;
}

.contrato-item.selecionado:hover {
  background: #1F5698;
}

.olt-item {
  padding: 5px 10px;
  border-radius: 5px;
  width: 230px;
  border: 4px solid #999;
  margin-right: 15px;
}

.olt-item:hover {
  cursor: pointer;
  background: #D9D9D9;
}

.olt-item.selected {
  background: #FFF;
  border-color: #39F;
  border-width: 4px;
}

.olt-item.disabled:hover {
  cursor: not-allowed;
}

.shown-password-td {
  padding-left: 30px !important;
}

#main-navbar.collapse.in, #main-navbar.collapsing {
  padding-top: 15px;
}

.onoffswitch {
  position: relative;
  width: 100px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.onoffswitch-checkbox {
  display: none;
}

.onoffswitch-label {
  display: block;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid #999999;
  border-radius: 5px;
}

.onoffswitch-inner {
  display: block;
  width: 200%;
  margin-left: -100%;
  -moz-transition: margin 0.3s ease-in 0s;
  -webkit-transition: margin 0.3s ease-in 0s;
  -o-transition: margin 0.3s ease-in 0s;
  transition: margin 0.3s ease-in 0s;
}

.onoffswitch-inner:before,
.onoffswitch-inner:after {
  display: block;
  float: left;
  width: 50%;
  height: 22px;
  padding: 0;
  line-height: 22px;
  font-size: 10px;
  color: white;
  font-family: Trebuchet, Arial, sans-serif;
  font-weight: bold;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.onoffswitch-inner:before {
  content: "ATIVADO";
  padding-right: 13px;
  background-color: #123774;
  color: #FFFFFF;
}

.onoffswitch-inner:after {
  content: "DESATIVADO";
  padding-right: 8px;
  background-color: #EEEEEE;
  color: #999999;
  text-align: right;
}

.onoffswitch-switch {
  display: block;
  width: 18px;
  margin: 0px;
  background: #FFFFFF;
  border: 2px solid #999999;
  border-radius: 5px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 82px;
  -moz-transition: all 0.3s ease-in 0s;
  -webkit-transition: all 0.3s ease-in 0s;
  -o-transition: all 0.3s ease-in 0s;
  transition: all 0.3s ease-in 0s;
  background-image: -moz-linear-gradient(center top, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
  background-image: -webkit-linear-gradient(center top, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
  background-image: -o-linear-gradient(center top, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
  background-image: linear-gradient(center top, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
}

.onoffswitch-checkbox:checked+.onoffswitch-label .onoffswitch-inner {
  margin-left: 0;
}

.onoffswitch-checkbox:checked+.onoffswitch-label .onoffswitch-switch {
  right: 0px;
}

figure {
  border: 1px solid;
  display: inline-block;
  margin: 0;
  position: relative;
  text-decoration: none;
  color: white;
  text-align: center;
}

figcaption {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color:rgba(0, 0, 0, 0.50);
  width: 100%;
  font-weight: bold;
}

.panel-login{

    margin-top: 50%;
}

.input-group-addon {
  padding: 2px 12px;
}

map, #streetview {display:inline-block !important;width:100%;height: 600px}

.panel-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 12px;
    color: inherit;
}

.panel-body {
  padding: 10px;
}

.panel-heading {
  padding: 4px 15px;
}

.panel.data-panel > .panel-body {
  padding: 0px 10px;
  padding-top: 5px;
}

.label-warning {
    background-color: rgb(255, 177, 0);
}

.png-time.form-control {
  padding-left: 10px;
}

.png-time input {
  border: none;
  width: 13px;
  padding-top: 4px;
  margin: 0;
  text-align: center;
  cursor: default;
}

.png-time input:focus {
  color: #000;
}

.png-time input.mode {
  width: 22px;
  margin-left: 3px;
}

#loading-bar-spinner {
  top: 130px;
  left: 10px;
}

#loading-bar-spinner .spinner-icon {
  width: 50px;
  height: 50px;
  border:  solid 5px transparent;
  border-top-color:  #29d;
  border-left-color: #29d;
  border-radius: 50%;

  -webkit-animation: loading-bar-spinner 400ms linear infinite;
  -moz-animation:    loading-bar-spinner 400ms linear infinite;
  -ms-animation:     loading-bar-spinner 400ms linear infinite;
  -o-animation:      loading-bar-spinner 400ms linear infinite;
  animation:         loading-bar-spinner 400ms linear infinite;
}

#loading-bar .bar {
  -webkit-transition: width 350ms;
  -moz-transition: width 350ms;
  -o-transition: width 350ms;
  transition: width 350ms;

  background: #29d;
  position: fixed;
  z-index: 1000;
  top: 110px;
  left: 0;
  width: 100%;
  height: 5px;
  border-bottom-right-radius: 1px;
  border-top-right-radius: 1px;
}

.dropdown-submenu {
    position: relative;
}

.dropdown-submenu>.dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: -1px;
    -webkit-border-radius: 0 6px 6px 6px;
    -moz-border-radius: 0 6px 6px;
    border-radius: 0 6px 6px 6px;
}

.dropdown-submenu:hover>.dropdown-menu {
    display: block;
}

.dropdown-submenu>a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #ccc;
    margin-top: 5px;
    margin-right: -10px;
}

.dropdown-submenu:hover>a:after {
    border-left-color: #fff;
}

.dropdown-submenu.pull-left {
    float: none;
}

.dropdown-submenu.pull-left>.dropdown-menu {
    left: -100%;
    margin-left: 10px;
    -webkit-border-radius: 6px 0 6px 6px;
    -moz-border-radius: 6px 0 6px 6px;
    border-radius: 6px 0 6px 6px;
}

.progress {
    margin-bottom: 0px;
}

.strike {
    text-decoration: line-through;
}

.info-number {
  font-size: 10px;
font-weight: normal;
line-height: 13px;
padding: 2px 2px;
position: absolute;
right: 0px;
top: 3px;
background-color: #D80F0F;
}

.top-buffer { margin-top:20px; }

.sub-nav{
    margin: 0;
    padding: 0;
    min-height: 20px;
    border-radius: 0;
    position: fixed;
    right: 0;
    left: 0;
    top: 50px;
    z-index: 4;
}

.sub-nav-evento{
    margin: 0;
    padding: 0;
    min-height: 30px;
    border-radius: 0;
    position: fixed;
    right: 0;
    left: 0;
    top: 82px;
    z-index: 4;
    color:#fff;
}


.modal {
    -webkit-transition:all linear 0.5s;
    transition:all linear 0.5s;
}
.modal.ng-hide {
    opacity:0;
}

.nya-bs-select.form-control:not([class*=col-]) {
    width: auto;
}
.input-group.input-group-unstyled input.form-control {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}
.input-group-unstyled .input-group-addon {
    border-radius: 4px;
    border: 0px;
    background-color: transparent;
}

.select2 > .select2-choice.ui-select-match {
    /* Because of the inclusion of Bootstrap */
    height: 29px;
}

.selectize-control > .selectize-dropdown {
    top: 36px;
}
.dropdown.dropdown-lg .dropdown-menu {
    margin-top: -1px;
    padding: 6px 20px;
}
.input-group-btn .btn-group {
    display: flex !important;
}
.btn-group .btn {
    border-radius: 0;
    margin-left: -1px;
}
.btn-group .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
.btn-group .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
.btn-group .form-horizontal .btn[type="submit"] {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
.form-horizontal .form-group {
    margin-left: 0;
    margin-right: 0;
}
.form-group .form-control:last-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

@media screen and (min-width: 768px) {
    #nome {
        width: 385px;
        margin: 0 auto;
    }
    .dropdown.dropdown-lg {
        position: static !important;
    }
    .dropdown.dropdown-lg .dropdown-menu {
        min-width: 385px;
    }
}

.angular-ui-tree-handle {
    background: #f8faff;
    border: 1px solid #dae2ea;
    color: #7c9eb2;
    padding: 10px 10px;
}

.angular-ui-tree-handle:hover {
    color: #438eb9;
    background: #f4f6f7;
    border-color: #dce2e8;
}

.angular-ui-tree-placeholder {
    background: #f0f9ff;
    border: 2px dashed #bed2db;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}


.group-title {
    background-color: #687074 !important;
    color: #FFF !important;
}

.tabela-eventos {
    table-layout:fixed;
}

.tabela-eventos td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.gi-2x{font-size: 2em;}
.gi-3x{font-size: 3em;}
.gi-4x{font-size: 4em;}
.gi-5x{font-size: 5em;}

.xx-dialog .modal-dialog {
    width :85%;

}

.btn-file {
    position: relative;
    overflow: hidden;
}
.btn-file input[type=file] {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 100%;
    min-height: 100%;
    font-size: 100px;
    text-align: right;
    filter: alpha(opacity=0);
    opacity: 0;
    outline: none;
    background: white;
    cursor: inherit;
    display: block;
}



.table tbody>tr>td.vert-align{
    vertical-align: middle;
}
.form_ptmp .modal-content {
    width: 1000px;
    margin-left: -200px;
}

.tabela-clientes table {
    width: 100%;
}



.tabela-clientes thead,
.tabela-clientes tbody,
.tabela-clientes tr,
.tabela-clientes td,
.tabela-clientes th { display: block; }

.tabela-clientes tr:after {
    content: ' ';
    display: block;
    visibility: hidden;
    clear: both;
}

.tabela-clientes thead th {
    height: 30px;

    /*text-align: left;*/
}

.tabela-clientes tbody {
    height: 320px;
    overflow-y: auto;
}

.tabela-clientes thead {
    /* fallback */
}

.tabela-clientes tbody td, .tabela-clientes thead th {
    width: 19.2%;
    float: left;
}

.tabela-notas .table th, .table td {
    border-top: none !important;
    width:auto;
}

.tabela-notas .table tbody>tr>td.align-left{
    align: left;
}

.panel-default>.panel-heading {
    background-color: #E0E0E0;
}

body {
    padding-top: 110px;
}

.subnav {
    min-height: 30px;
    width: 500px;
}

.linhaSelecionada {
    background-color: #4C4CFF !important;
}


.form-signin {
    max-width: 330px;
    padding: 15px;
    margin: 0 auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.form-signin .form-signin-heading,
.form-signin .checkbox {
    margin-bottom: 10px;
}
.form-signin .checkbox {
    font-weight: normal;
    position: relative;
}
.form-signin .form-control {

    height: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px;
    font-size: 12px;

}
.form-signin .form-control:focus {
    z-index: 2;
}
.form-signin input[type="email"] {
    margin-bottom: -1px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.form-signin input[type="password"] {
    margin-bottom: 10px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}


/*
 * Base structure
 */



/*
 * Global add-ons
 */

.sub-header {
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

/*
 * Top navigation
 * Hide default border to remove 1px line.
 */
.navbar-fixed-top {
  border: 0;
}

.navbar-default {
  background-color: #2fa4e7;
  border-color: #1995dc;
}

.navbar-default .navbar-nav>li>a {
  color: #fff;
}

.navbar-default .navbar-brand, .navbar-default .navbar-nav>li>a {
  color: #fff;
}

.navbar-inverse {
  background-color: #033c73;
  border-color: #022f5a;
}

.navbar-default .btn-link, .navbar-default .navbar-link, .navbar-default .navbar-link:hover, .navbar-inverse .navbar-brand, .navbar-inverse .navbar-nav>li>a, .navbar-inverse .navbar-text {
  color: #fff;
}

.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:focus, .navbar-default .navbar-nav>.active>a:hover {
  color: #fff;
    background-color: #178acc;
}

.navbar-default .navbar-nav>a, .navbar-default .navbar-nav>a:focus, .navbar-default .navbar-nav>a:hover {
  color: #fff;
    background-color: #178acc;
}

.navbar-default .navbar-nav>li>a:hover {
  background-color: #178acc;
  color: #fff;
}

.navbar-inverse .navbar-nav>li>a:hover {
  background-color: #178acc;
  color: #fff;
}

.navbar-inverse .navbar-nav>a:hover {
  color: #fff;
  background-color: #080808;
}

hr.message-inner-separator
{
    clear: both;
    margin-top: 10px;
    margin-bottom: 13px;
    border: 0;
    height: 1px;
    background-image: -webkit-linear-gradient(left,rgba(0, 0, 0, 0),rgba(0, 0, 0, 0.15),rgba(0, 0, 0, 0));
    background-image: -moz-linear-gradient(left,rgba(0,0,0,0),rgba(0,0,0,0.15),rgba(0,0,0,0));
    background-image: -ms-linear-gradient(left,rgba(0,0,0,0),rgba(0,0,0,0.15),rgba(0,0,0,0));
    background-image: -o-linear-gradient(left,rgba(0,0,0,0),rgba(0,0,0,0.15),rgba(0,0,0,0));
}


/*
 * Sidebar
 */

/* Hide for mobile, show later */
.sidebar {
  display: none;
}
@media (min-width: 768px) {
  .sidebar {
    position: fixed;
    top: 51px;
    bottom: 0;
    left: 0;
    z-index: 1000;
    display: block;
    padding: 20px;
    overflow-x: hidden;
    overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */
    background-color: #f5f5f5;
    border-right: 1px solid #eee;
  }
}

/* Sidebar navigation */
.nav-sidebar {
  margin-right: -21px; /* 20px padding + 1px border */
  margin-bottom: 20px;
  margin-left: -20px;
}
.nav-sidebar > li > a {
  padding-right: 20px;
  padding-left: 20px;
}
.nav-sidebar > .active > a,
.nav-sidebar > .active > a:hover,
.nav-sidebar > .active > a:focus {
  color: #fff;
  background-color: #428bca;
}


/*
 * Main content
 */

.main {
  padding: 20px;
}
@media (min-width: 768px) {
  .main {
    padding-right: 40px;
    padding-left: 40px;
  }
}
.main .page-header {
  margin-top: 0;
}


/*
 * Placeholder dashboard ideas
 */

.placeholders {
  margin-bottom: 30px;
  text-align: center;
}
.placeholders h4 {
  margin-bottom: 0;
}
.placeholder {
  margin-bottom: 20px;
}
.placeholder img {
  display: inline-block;
  border-radius: 50%;
}

.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}

.thumbnail {
  height: 200px;
}

.thumbnail img.pull-right {
  width: 50px;
}

.select2 > .select2-choice.ui-select-match {
  /* Because of the inclusion of Bootstrap */
  height: 29px;
}

.selectize-control > .selectize-dropdown {
  top: 36px;
}
.dropdown.dropdown-lg .dropdown-menu {
  margin-top: -1px;
  padding: 6px 20px;
}
.input-group-btn .btn-group {
  display: flex !important;
}
.btn-group .btn {
  border-radius: 0;
  margin-left: -1px;
}
.btn-group .btn:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.btn-group .form-horizontal .btn[type="submit"] {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.form-horizontal .form-group {
  margin-left: 0;
  margin-right: 0;
}
.form-group .form-control:last-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

@media screen and (min-width: 768px) {
  #nome {
    width: 385px;
    margin: 0 auto;
  }
  .dropdown.dropdown-lg {
    position: static !important;
  }
  .dropdown.dropdown-lg .dropdown-menu {
    min-width: 385px;
  }
}

.angular-ui-tree-handle {
  background: #f8faff;
  border: 1px solid #dae2ea;
  color: #7c9eb2;
  padding: 10px 10px;
}

.angular-ui-tree-handle:hover {
  color: #438eb9;
  background: #f4f6f7;
  border-color: #dce2e8;
}

.angular-ui-tree-placeholder {
  background: #f0f9ff;
  border: 2px dashed #bed2db;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}


.group-title {
  background-color: #687074 !important;
  color: #FFF !important;
}

.tabela-eventos {
  table-layout:fixed;
}

.tabela-eventos td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gi-2x{font-size: 2em;}
.gi-3x{font-size: 3em;}
.gi-4x{font-size: 4em;}
.gi-5x{font-size: 5em;}

.xx-dialog .modal-dialog {
  width :85%;

}

.btn-file {
  position: relative;
  overflow: hidden;
}
.btn-file input[type=file] {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 100%;
  min-height: 100%;
  font-size: 100px;
  text-align: right;
  filter: alpha(opacity=0);
  opacity: 0;
  outline: none;
  background: white;
  cursor: inherit;
  display: block;
}



.table tbody>tr>td.vert-align{
  vertical-align: middle;
}
.form_ptmp .modal-content {
  width: 1000px;
  margin-left: -200px;
}

.tabela-clientes table {
  width: 100%;
}



.tabela-clientes thead,
.tabela-clientes tbody,
.tabela-clientes tr,
.tabela-clientes td,
.tabela-clientes th { display: block; }

.tabela-clientes tr:after {
  content: ' ';
  display: block;
  visibility: hidden;
  clear: both;
}

.tabela-clientes thead th {
  height: 30px;

  /*text-align: left;*/
}

.tabela-clientes tbody {
  height: 320px;
  overflow-y: auto;
}

.tabela-clientes thead {
  /* fallback */
}

.tabela-clientes tbody td, .tabela-clientes thead th {
  width: 19.2%;
  float: left;
}

tabela-notas .table th, .table td {
  border-top: none !important;
  width:auto;
}

tabela-notas .table tbody>tr>td.align-left{
  align: left;
}

.panel-default>.panel-heading {
  background-color: #E0E0E0;
}

.subnav {
  min-height: 30px;
  width: 500px;
}

.linhaSelecionada {
  background-color: #4C4CFF !important;
}


.form-signin {
  max-width: 330px;
  padding: 15px;
  margin: 0 auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.form-signin .form-signin-heading,
.form-signin .checkbox {
  margin-bottom: 10px;
}

.form-signin .form-control {

  height: auto;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px;
  font-size: 12px;

}
.form-signin .form-control:focus {
  z-index: 2;
}
.form-signin input[type="email"] {
  margin-bottom: -1px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.form-signin input[type="password"] {
  margin-bottom: 10px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

tags-input {
  box-shadow: none;
  border: none;
  padding: 0;
  min-height: 34px;
}
tags-input .host {
  margin: 0;
}
tags-input .tags {
  -moz-appearance: none;
  -webkit-appearance: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  -moz-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
tags-input .tags .tag-item {
  color: #fff;
  background: #428bca;
  border: 1px solid #357ebd;
  border-radius: 4px;
}
tags-input .tags .tag-item.selected {
  color: #fff;
  background: #d9534f;
  border: 1px solid #d43f3a;
}
tags-input .tags .tag-item .remove-button:hover {
  text-decoration: none;
}
tags-input .tags.focused {
  border: 1px solid #66afe9;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
tags-input .autocomplete {
  border-radius: 4px;
}
tags-input .autocomplete .suggestion-item.selected {
  color: #262626;
  background-color: #f5f5f5;
}
tags-input .autocomplete .suggestion-item.selected em {
  color: #262626;
  background-color: #f5f5f5;
}
tags-input .autocomplete .suggestion-item em {
  color: #000;
  background-color: #fff;
}
tags-input.ng-invalid .tags {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
tags-input[disabled] .tags {
  background-color: #eee;
}
tags-input[disabled] .tags .tag-item {
  background: #337ab7;
  opacity: 0.65;
}
tags-input[disabled] .tags .input {
  background-color: #eee;
}

.input-group tags-input {
  padding: 0;
  display: table-cell;
}
.input-group tags-input:not(:first-child) .tags {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group tags-input:not(:last-child) .tags {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group-lg tags-input:first-child .tags {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.input-group-lg tags-input:last-child .tags {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.input-group-sm tags-input:first-child .tags {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.input-group-sm tags-input:last-child .tags {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

tags-input.ti-input-lg, .input-group-lg tags-input {
  min-height: 46px;
}
tags-input.ti-input-lg .tags, .input-group-lg tags-input .tags {
  border-radius: 6px;
}
tags-input.ti-input-lg .tags .tag-item, .input-group-lg tags-input .tags .tag-item {
  height: 38px;
  line-height: 37px;
  font-size: 18px;
  border-radius: 6px;
}
tags-input.ti-input-lg .tags .tag-item .remove-button, .input-group-lg tags-input .tags .tag-item .remove-button {
  font-size: 20px;
}
tags-input.ti-input-lg .tags .input, .input-group-lg tags-input .tags .input {
  height: 38px;
  font-size: 18px;
}
tags-input.ti-input-sm, .input-group-sm tags-input {
  min-height: 30px;
}
tags-input.ti-input-sm .tags, .input-group-sm tags-input .tags {
  border-radius: 3px;
}
tags-input.ti-input-sm .tags .tag-item, .input-group-sm tags-input .tags .tag-item {
  height: 22px;
  line-height: 21px;
  font-size: 12px;
  border-radius: 3px;
}
tags-input.ti-input-sm .tags .tag-item .remove-button, .input-group-sm tags-input .tags .tag-item .remove-button {
  font-size: 16px;
}
tags-input.ti-input-sm .tags .input, .input-group-sm tags-input .tags .input {
  height: 22px;
  font-size: 12px;
}

.has-feedback tags-input .tags {
  padding-right: 30px;
}

.has-success tags-input .tags {
  border-color: #3c763d;
}
.has-success tags-input .tags.focused {
  border-color: #2b542c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}

.has-error tags-input .tags {
  border-color: #a94442;
}
.has-error tags-input .tags.focused {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}

.has-warning tags-input .tags {
  border-color: #8a6d3b;
}
.has-warning tags-input .tags.focused {
  border-color: #66512c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}

.pointer {
  cursor: pointer;
}

div.table {
  display: table;
  margin-bottom: 0px;
}

div.tr {
  display: table-row;
}

div.td {
  display: table-cell;
}

.align-center, table.align-center td, table.align-center th {
  text-align: center;
}

.align-right, table.align-right td, table.align-right th {
  text-align: right;
}

.align-left, table.align-left td, table.align-left th {
  text-align: left;
}

.valign-top, table.valign-top td, table.valign-top th {
  vertical-align: top !important;
}

.valign-middle, table.valign-middle td, table.valign-middle th {
  vertical-align: middle !important;
}

.valign-bottom, table.valign-bottom td, table.valign-bottom th {
  vertical-align: bottom !important;
}

table.table-centered td, table.table-centered th {
  vertical-align: middle;
  text-align: center;
}

table.spaced-td td, table.spaced-td th {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.btn-vsm {
  padding: 2px 4px !important;
  line-height: 1;
}

.btn-icon {
  margin-right: 8px;
}

.btn-sm-icon {
  margin-right: 6px;
}

.container.alocar-router {
  width: 100%;
  padding: 0px;
  border: 1px solid #CCC;
  margin-bottom: 15px;
}

.no-margin {
  margin: 0px !important;
}

.no-padding {
  padding: 0px !important;
}

div.td.centered, td.centered, th.centered {
  vertical-align: middle !important;
  text-align: center;
}

.half-width {
  width: 50% !important;
}

.full-width {
  width: 100% !important;
}

table.bottom-spaced tr > td {
  padding-bottom: 10px !important;
}

table.bottom-spaced tr:last-child > td {
  padding-bottom: 0px !important;
}

.table-justified tr > td:first-child {
  width: 50%;
  text-align: left;
}

.table-justified tr > td:last-child {
  width: 50%;
  text-align: right;
}

table.align-right tr > td {
  text-align: right;
}

span.copier {
  color: #2d76d0;
}

span.copier:hover {
  cursor: pointer;
  text-decoration: underline;
}

tr.desativado {
  background: #ff7777 !important;
  color: #FFF;
}

.bg-white {
  background: #FFF;
}

.bg-success-dark {
  background: #369e14;
  color: #FFF;
}

tr.bg-warning {
  background: #fefce4;
  color: #634909;
}

.bg-warning-dark {
  background: #f77002;
  color: #FFF;
}

.bg-danger-dark {
  background: #d60a0a;
  color: #FFF;
}

.reconexoes-container {
  display: inline-block;
  font-size: 10pt;
  padding: 2px 10px;
  border-radius: 5px;
  border: 1px solid #666;
  margin: 0 auto;
  font-weight: bold;
}

.reconexoes-container > span {
  border: 1px solid #333;
  background: #FFF;
  color: #000;
  font-weight: normal;
  font-size: 10pt;
}

.wifi-config-container {
  background: #F3F3F3;
  padding: 10px;
  padding-bottom: 0px;
}

.wifi-config-container table {
  width: 100%;
  margin: 0 auto;
}


.operation-config-container {
  display: inline-block;
  margin-right: 10px;
  border: 1px solid #DDD;
  background: #F3F3F3;
  width: 230px;;
  padding: 10px;
  border-radius: 5px;
  padding-bottom: 0px;
}

.no-pointer {
  cursor: default;
}

.btn-vsm {
  width: 25px;
  height: 20px;
  padding: 2px 0px 0px 2px;
}

button.btn-smaller {
  padding: 2px 4px !important;
  line-height: 1;
}

.btn-vvsm {
  width: 20px;
  height: 16px;
  font-size:8pt !important;
  padding: 1px 0px 0px 0px !important;
}

.font9 {
  font-size: 9pt !important
}

.font10 {
  font-size: 10pt !important
}

.font11 {
  font-size: 11pt !important;
}

.horizontal-divider {
  width: 100%;
  height: 1px;
  background: #ddd;
  margin: 15px 0px;
}

.horizontal-divider.spacing-sm {
  margin: 5px 0px;
}

.radio-table {
  width: 100%;
  border-radius: 3px !important;
}

.radio-table td {
  border: 1px solid #999 !important;
  text-align: center;
  width: 50%;
  cursor: pointer;
  padding: 2px 0px;
  border-radius: 3px 0px 0px 3px !important;
  font-size: 7pt;
  background: #EEE;
}

.radio-table td.active {
  font-weight: bold;
}

.radio-table td.success.active {
  background: #5DB85C;
  color: #FFF;
}

.radio-table td.danger.active {
  background: #D9534F;
  color: #FFF;
}

.radio-table td.success.active:hover {
  background: #4CAA4B;
  color: #FFF;
}

.radio-table td.danger.active:hover {
  background: #C8423E;
  color: #FFF;
}

.radio-table:not(.disabled) td:hover {
  background: rgba(0, 0, 0, 0.1);
}

.radio-table td.not-available, .radio-table td.not-available:hover {
  font-weight: bold;
  width: 100%;
  cursor: default;
  background: #D9534F;
  color: #FFF;
}

.radio-table.disabled td {
  cursor: default;
}

.radio-table.disabled td:hover {
  cursor: default;

}

.modal.vcentered {
  text-align: center;
  padding: 0!important;
}

.modal.vcentered:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  /*margin-right: -4px; /* Adjusts for spacing */
}

.modal.vcentered .modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

.margin-top-10 {
  margin-top: 10px;
}
/*
.hd-alerts-container {
  width: 100%;
  margin-bottom: 5px;
  background: #FFF;
}*/

.hd-alerts-container {
  display: flex;
  align-content: center;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
  background: #FFF;
}

.hd-alert {
  display: inline-block;
  font-weight: bold;
  padding: 3px 10px;
  max-width: 180px;
  text-align: center;
  border: 1px solid #999;
  border-radius: 3px;
}

.hd-alert-status {
  width: auto;
  margin: 0 auto;
  font-size: 7pt;
  letter-spacing: 1px;
  margin-bottom: 3px;
  background: #FFF;
  border-radius: 3px; padding: 1px 3px;
}

.hd-alert-status.ativo {
  color: #F00;
}

.hd-alert-status.finalizado {
  color: #f77002;
}

.hd-alert-data {
  font-size: 15pt;
}

.hd-alert.pointer:hover {
  -webkit-filter: brightness(70%);
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  transition: all 0.5s ease;
}

.hd-alert.small {
  margin-top: 0px;
}

.flex-alert {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.ixc-alert-data {
  font-size: 7pt;
}

.alerts-fieldset {
  border: 1px solid #000;
  padding: 3px;
  padding-top: 0px;
  display: flex;
  gap: 3px;
}

.alerts-fieldset legend {
  font-size: 10pt;
  margin-bottom: 5px;
  border: none;
}

.com-alerts-container {
  width: auto;
  margin-bottom: 5px;
  background: #FFF;
}

.com-alert {
  display: inline-block;
  font-weight: bold;
  padding: 3px;
  width: 180px;
  height: 63px;
  text-align: center;
  border: 1px solid #999;
  border-radius: 3px;
}

.com-alert-data {
  font-size: 15pt;
}

.com-alert.pointer:hover {
  -webkit-filter: brightness(70%);
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

#operation-mode-log-container {
  min-height: 222px;
  border: 1px solid #999;
}

#operation-mode-log {
  border: 1px solid #AAA;
  width: 100%;
}

#operation-mode-log td, #operation-mode-log th {
  border: 1px solid #AAA;
  padding: 2px 4px;
  text-align: center;
}

#operation-mode-log thead tr:first-child th:first-child {
  background: #CCC;
  text-align: center;
}

#operation-mode-log thead tr:last-child th {
  background: #E0E0E0;
}

#operation-mode-log-container {
  overflow-y: auto;
  max-height: 222px;
}

.margin-right-5 {
  margin-right: 5px;
}

.margin-right-10 {
  margin-right: 10px;
}

.margin-left-5 {
  margin-left: 5px;
}

.margin-left-10 {
  margin-left: 10px;
}

.padding-right-5 {
  padding-right: 5px;
}

.padding-right-10 {
  padding-right: 10px;
}

.padding-left-5 {
  padding-left: 5px;
}

.padding-left-10 {
  padding-left: 10px;
}

.contrato-data-table-container {
  margin: 0 auto;
  margin-top: 15px;
  padding: 0px;
  border: 1px solid #000;
  width: 350px;
}

.contrato-data-table {
  margin: 0px;
  width: 350px;
}

.contrato-data-table td {
  text-align: left;
  vertical-align: middle !important;
  padding: 2px 5px !important;
  font-size: 10pt;
}

.filter-spaced-table td {
  padding: 3px 3px;
}

.filter-section-container .td {
  padding: 0px 5px;
}

.input-date-md {
  font-size: 10pt !important;
}

tr.hoverable:hover {
  background: rgba(0, 0, 0, 0.2) !important;
}

tr.selected, tr.selected:hover {
  background: #30A4E7 !important;
  color: #FFF;
}

span.label.white-bordered {
  border: 1px solid #FFF !important;
}

.nowrap{
  white-space: nowrap;
}

.self-to-center {
  margin: 0 auto;
}

.text-warning-dark {
  color: #ff6600;
}

.text-danger-dark {
  color: #ff0000;
}

.text-success-dark {
  color: #009933;
}

.conclusoes-select .nya-bs-select.btn-group .dropdown-menu.inner {
    min-height: 115px !important;
}

.btn-acs {
  background: #92254B;
  color: #FFF;
}

.btn-acs:hover, .btn-acs:focus {
  background: #851812;
  color: #FFF;
}

.progress-bordered {
  border: 1px solid #666;
}

div.progress-sm {
  height: 15px;
}
div.progress-sm div.progress-bar {
  line-height: 15px;
}

.flex.row {
  display: flex;
  flex-wrap: wrap;
}

.span-close-btn, .span-close-btn:hover {
  font-size: 7pt;
  border-radius: 2px;
  border: 1px solid #F00;
  padding-left: 3px;
  padding-right: 3px;
  margin-left: 3px;
  color: #F00;
  text-decoration: none;
}

.span-close-btn:hover {
  color: #FFF;
  border-color: #FFF;
}

.spinner-20 {
  width: 20px;
}

.spinner-30 {
  width: 30px;
}

.spinner-40 {
  width: 50px;
}

div.legenda {
  width: 13px;
  height: 13px;
  border: 1px solid #000;
  margin-right: 5px;
  margin-left: 15px;
  border-radius: 2px;
}

div.legenda.primary {
  background: #337AB7;
}

div.legenda.warning {
  background: #8B6D3B;
}

div.legenda.dark {
  background: #333;
}

.auto-width {
  width: auto;
}

.section-header {
  width: 100%;
  text-align: center;
  background: #DDD;
  padding: 6px;
  border-top: 1px solid #333;
}

.no-left-border, .no-left-border th, .no-left-border td {
  border-left: none !important;
}

.no-right-border, .no-right-border th, .no-right-border td {
  border-right: none !important;
}

li.highlight, li.highlight a {
  color: darkorange;
}

.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

#dados .col {
  border: 1px solid #CCC;
  border-radius: 2px;
  padding: 4px;
  min-height: 100%;
}

#dados .row {
  margin-left: -5px;
  margin-right: -5px;
}

#dados .row {
  display: flex;
  display: -webkit-flex;
  flex-wrap: wrap;
}

.pedido-documento-item {
  width: 200px !important;
  height: 200px !important;
  border: 1px solid #C0C0C0;
  margin: 1px;
}

.pedido-documento-item .file-extension {
  height: 183px;
  width: 200px !important;
  font-size: 15pt;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.pedido-documento-item .file-caption {
  height: 16px;
  background-color: rgba(0, 0, 0, 0.50);
  color: #FFF;
  font-weight: bold;
  text-decoration: none !important;
  text-align: center;
}

.pedido-documento-item:hover .file-caption {
  text-decoration: none !important;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.hd-section {
  border: 1px solid #CCC;
  border-radius: 3px;
  margin-bottom: 20px;
}

.hd-section .table, .hd-section table {
  margin-bottom: 0px;
}

.hd-section-title {
  text-align: center;
  font-weight: bold;
  /* text-transform: uppercase; */
  font-size: 11pt;
  background: #DDD;
  border-radius: 3px 3px 0px 0px;
  color: #444;
  padding: 0px 5px;
}

.hd-section-title img {
  width: 16px;
}

.hd-section-title > i.glyphicon, .hd-section-title > div > i.glyphicon {
  margin-right: 5px;
  font-size: 9pt;
}

.hd-section-title button {
  min-width: 20px;
}

.label.label-light {
  background: #FFF;
  color: #333;
  border: 1px solid #CCC;
}

.label.label-light.text-primary {
  color: #337AB7;
  border-color: #5e809e;
}

div.hd-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, #EEE, #CCC, #CCC, #CCC, #EEE);
  margin: 0 auto;
  margin-bottom: 15px;
}


.modal.fade.ng-isolate-scope {
  top: 30%;
}


#graficoTrafego .highcharts-container, #graficoPing .highcharts-container {
  width: 100%;
}


.copy-toast {
  
}